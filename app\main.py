import streamlit as st
from app.rag_engine import retrieve_and_generate
from app.sentiment import analyze_sentiment
from app.escalation import check_escalation
from app.empathy import generate_empathetic_response
from app.satisfaction import track_satisfaction

st.title('Customer Support RAG with Sentiment Analysis')

if 'history' not in st.session_state:
    st.session_state['history'] = []

user_input = st.text_input('How can we help you today?')

if user_input:
    sentiment = analyze_sentiment(user_input)
    escalation = check_escalation(st.session_state['history'], user_input)
    context, articles = retrieve_and_generate(user_input)
    response = generate_empathetic_response(user_input, context, sentiment, escalation)
    st.session_state['history'].append({'user': user_input, 'bot': response, 'sentiment': sentiment, 'escalation': escalation})
    st.markdown(f'**Bot:** {response}')
    track_satisfaction(st.session_state['history'])
