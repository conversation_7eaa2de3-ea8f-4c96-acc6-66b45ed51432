# Customer Support RAG with Sentiment Analysis

A Retrieval-Augmented Generation (RAG) system for customer support, featuring real-time sentiment analysis, escalation detection, and empathetic response generation.

## Features
- Help article retrieval using vector search (ChromaDB)
- Real-time sentiment and emotion analysis (HuggingFace Transformers)
- Escalation pattern recognition
- Empathetic, context-aware response generation
- Customer satisfaction tracking
- Streamlit demo UI

## Setup
1. Clone the repo
2. Install requirements: `pip install -r requirements.txt`
3. Run the app: `streamlit run app/main.py`

## Project Structure
- `app/`: Main application code
- `data/`: Knowledge base and processed data
- `models/`: Custom/downloaded models

## Customization
- Add your help articles to `data/articles/`
- Extend modules in `app/` for advanced features

## License
MIT
