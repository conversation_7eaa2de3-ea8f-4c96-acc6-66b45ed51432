"""
RAG Engine for Customer Support with Vector Database Integration
"""
import logging
from typing import List, Dict, Tuple

import chromadb
from chromadb.config import Settings
from sentence_transformers import SentenceTransformer

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DocumentChunker:
    """Handles document chunking strategies for different content types."""

    def __init__(self, chunk_size: int = 512, chunk_overlap: int = 50):
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap

    def chunk_text(self, text: str, metadata: Dict = None) -> List[Dict]:
        """
        Chunk text into overlapping segments optimized for customer support content.
        """
        if not text or len(text.strip()) == 0:
            return []

        # Split by sentences first to maintain semantic coherence
        sentences = text.split('. ')
        chunks = []
        current_chunk = ""
        current_length = 0

        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue

            sentence_length = len(sentence)

            # If adding this sentence would exceed chunk size, save current chunk
            if current_length + sentence_length > self.chunk_size and current_chunk:
                chunks.append({
                    'text': current_chunk.strip(),
                    'metadata': metadata or {},
                    'length': current_length
                })

                # Start new chunk with overlap
                overlap_text = self._get_overlap_text(current_chunk)
                current_chunk = overlap_text + sentence + '. '
                current_length = len(current_chunk)
            else:
                current_chunk += sentence + '. '
                current_length += sentence_length + 2

        # Add the last chunk if it exists
        if current_chunk.strip():
            chunks.append({
                'text': current_chunk.strip(),
                'metadata': metadata or {},
                'length': current_length
            })

        return chunks

    def _get_overlap_text(self, text: str) -> str:
        """Get overlap text from the end of current chunk."""
        if len(text) <= self.chunk_overlap:
            return text
        return text[-self.chunk_overlap:] + " "


class RAGEngine:
    """
    Retrieval-Augmented Generation Engine for Customer Support.
    Handles document indexing, retrieval, and context generation.
    """

    def __init__(self,
                 model_name: str = "all-MiniLM-L6-v2",
                 collection_name: str = "customer_support_kb",
                 persist_directory: str = "./data/chroma_db"):

        self.model_name = model_name
        self.collection_name = collection_name
        self.persist_directory = persist_directory

        # Initialize embedding model
        logger.info(f"Loading embedding model: {model_name}")
        self.embedding_model = SentenceTransformer(model_name)

        # Initialize ChromaDB
        self.client = chromadb.PersistentClient(
            path=persist_directory,
            settings=Settings(anonymized_telemetry=False)
        )

        # Initialize or get collection
        try:
            self.collection = self.client.get_collection(collection_name)
            logger.info(f"Loaded existing collection: {collection_name}")
        except:
            self.collection = self.client.create_collection(
                name=collection_name,
                metadata={"description": "Customer support knowledge base"}
            )
            logger.info(f"Created new collection: {collection_name}")

        # Initialize chunker
        self.chunker = DocumentChunker()

        # Cache for embeddings
        self._embedding_cache = {}

    def add_documents(self, documents: List[Dict]) -> None:
        """
        Add documents to the knowledge base.

        Args:
            documents: List of documents with 'content', 'title', 'category', etc.
        """
        logger.info(f"Adding {len(documents)} documents to knowledge base")

        all_chunks = []
        all_embeddings = []
        all_metadatas = []
        all_ids = []

        for doc_idx, doc in enumerate(documents):
            content = doc.get('content', '')
            title = doc.get('title', f'Document {doc_idx}')
            category = doc.get('category', 'general')

            # Create metadata
            base_metadata = {
                'title': title,
                'category': category,
                'doc_id': doc_idx,
                'source': doc.get('source', 'unknown')
            }

            # Chunk the document
            chunks = self.chunker.chunk_text(content, base_metadata)

            for chunk_idx, chunk in enumerate(chunks):
                chunk_id = f"doc_{doc_idx}_chunk_{chunk_idx}"
                chunk_text = chunk['text']

                # Generate embedding
                embedding = self.embedding_model.encode(chunk_text).tolist()

                # Prepare metadata
                metadata = chunk['metadata'].copy()
                metadata.update({
                    'chunk_id': chunk_idx,
                    'chunk_length': chunk['length']
                })

                all_chunks.append(chunk_text)
                all_embeddings.append(embedding)
                all_metadatas.append(metadata)
                all_ids.append(chunk_id)

        # Add to ChromaDB
        if all_chunks:
            self.collection.add(
                documents=all_chunks,
                embeddings=all_embeddings,
                metadatas=all_metadatas,
                ids=all_ids
            )
            logger.info(f"Successfully added {len(all_chunks)} chunks to knowledge base")

    def retrieve_relevant_context(self,
                                query: str,
                                top_k: int = 5,
                                score_threshold: float = 0.3) -> Tuple[str, List[Dict]]:
        """
        Retrieve relevant context for a given query.

        Args:
            query: User query
            top_k: Number of top results to retrieve
            score_threshold: Minimum similarity score threshold

        Returns:
            Tuple of (assembled_context, retrieved_articles)
        """
        logger.info(f"Retrieving context for query: {query[:100]}...")

        # Generate query embedding
        query_embedding = self.embedding_model.encode(query).tolist()

        # Search in ChromaDB
        results = self.collection.query(
            query_embeddings=[query_embedding],
            n_results=top_k,
            include=['documents', 'metadatas', 'distances']
        )

        if not results['documents'] or not results['documents'][0]:
            logger.warning("No relevant documents found")
            return "No relevant information found in the knowledge base.", []

        # Process results
        retrieved_articles = []
        context_parts = []

        documents = results['documents'][0]
        metadatas = results['metadatas'][0]
        distances = results['distances'][0]

        for doc, metadata, distance in zip(documents, metadatas, distances):
            # Convert distance to similarity score (ChromaDB uses L2 distance)
            similarity_score = 1 / (1 + distance)

            if similarity_score >= score_threshold:
                article_info = {
                    'content': doc,
                    'title': metadata.get('title', 'Unknown'),
                    'category': metadata.get('category', 'general'),
                    'similarity_score': similarity_score,
                    'source': metadata.get('source', 'unknown')
                }
                retrieved_articles.append(article_info)

                # Add to context with source attribution
                context_parts.append(
                    f"[{metadata.get('title', 'Unknown')}]: {doc}"
                )

        # Assemble final context
        if context_parts:
            assembled_context = "\n\n".join(context_parts)
        else:
            assembled_context = "No sufficiently relevant information found."

        logger.info(f"Retrieved {len(retrieved_articles)} relevant articles")
        return assembled_context, retrieved_articles

    def get_collection_stats(self) -> Dict:
        """Get statistics about the knowledge base collection."""
        try:
            count = self.collection.count()
            return {
                'total_chunks': count,
                'collection_name': self.collection_name,
                'model_name': self.model_name
            }
        except Exception as e:
            logger.error(f"Error getting collection stats: {e}")
            return {'error': str(e)}


# Global RAG engine instance
_rag_engine = None

def get_rag_engine() -> RAGEngine:
    """Get or create the global RAG engine instance."""
    global _rag_engine
    if _rag_engine is None:
        _rag_engine = RAGEngine()
    return _rag_engine


def retrieve_and_generate(query: str, top_k: int = 5) -> Tuple[str, List[Dict]]:
    """
    Main function for retrieving and generating context.

    Args:
        query: User query
        top_k: Number of top results to retrieve

    Returns:
        Tuple of (context, articles)
    """
    try:
        rag_engine = get_rag_engine()
        context, articles = rag_engine.retrieve_relevant_context(query, top_k)
        return context, articles
    except Exception as e:
        logger.error(f"Error in retrieve_and_generate: {e}")
        return f"Error retrieving information: {str(e)}", []


def initialize_knowledge_base(force_reload: bool = False) -> None:
    """Initialize the knowledge base with sample data if needed."""
    rag_engine = get_rag_engine()

    # Check if knowledge base is already populated
    stats = rag_engine.get_collection_stats()
    if stats.get('total_chunks', 0) > 0 and not force_reload:
        logger.info(f"Knowledge base already contains {stats['total_chunks']} chunks")
        return

    # Load sample documents (this will be expanded in the next step)
    sample_documents = _get_sample_documents()

    if sample_documents:
        rag_engine.add_documents(sample_documents)
        logger.info("Knowledge base initialized with sample documents")
    else:
        logger.warning("No sample documents found to initialize knowledge base")


def _get_sample_documents() -> List[Dict]:
    """Get sample documents for initial knowledge base setup."""
    # This is a placeholder - will be expanded when we create the knowledge base
    return [
        {
            'title': 'Account Login Issues',
            'category': 'authentication',
            'content': '''If you're having trouble logging into your account, please try the following steps:
            1. Verify your email address and password are correct
            2. Check if Caps Lock is enabled
            3. Clear your browser cache and cookies
            4. Try using an incognito/private browsing window
            5. Reset your password if needed
            If you continue to experience issues, please contact our support team.''',
            'source': 'help_center'
        },
        {
            'title': 'Password Reset Process',
            'category': 'authentication',
            'content': '''To reset your password:
            1. Go to the login page and click "Forgot Password"
            2. Enter your email address
            3. Check your email for a reset link
            4. Click the link and create a new password
            5. Your new password must be at least 8 characters long
            Password reset links expire after 24 hours for security reasons.''',
            'source': 'help_center'
        }
    ]
