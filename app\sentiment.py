"""
Sentiment Analysis and Emotion Detection Module
Provides real-time sentiment analysis and mood detection for customer support.
"""
import logging
from typing import Dict, List, Tuple
import re

from transformers import pipeline, AutoTokenizer, AutoModelForSequenceClassification
from vaderSentiment.vaderSentiment import SentimentIntensityAnalyzer
from textblob import TextBlob

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SentimentAnalyzer:
    """
    Comprehensive sentiment analysis using multiple approaches:
    1. VADER for social media text and informal language
    2. RoBERTa for more nuanced sentiment analysis
    3. Emotion detection for empathy modeling
    """

    def __init__(self):
        self.vader_analyzer = SentimentIntensityAnalyzer()

        # Initialize emotion detection pipeline
        try:
            self.emotion_pipeline = pipeline(
                "text-classification",
                model="j-hartmann/emotion-english-distilroberta-base",
                return_all_scores=True
            )
            logger.info("Emotion detection model loaded successfully")
        except Exception as e:
            logger.warning(f"Could not load emotion model: {e}")
            self.emotion_pipeline = None

        # Initialize sentiment pipeline with RoBERTa
        try:
            self.sentiment_pipeline = pipeline(
                "sentiment-analysis",
                model="cardiffnlp/twitter-roberta-base-sentiment-latest",
                return_all_scores=True
            )
            logger.info("RoBERTa sentiment model loaded successfully")
        except Exception as e:
            logger.warning(f"Could not load RoBERTa model: {e}")
            self.sentiment_pipeline = None

        # Escalation keywords and patterns
        self.escalation_keywords = {
            'high': ['angry', 'furious', 'outraged', 'disgusted', 'hate', 'terrible',
                    'awful', 'worst', 'horrible', 'unacceptable', 'ridiculous'],
            'medium': ['frustrated', 'annoyed', 'disappointed', 'upset', 'concerned',
                      'worried', 'confused', 'bothered', 'irritated'],
            'urgent': ['urgent', 'emergency', 'asap', 'immediately', 'critical',
                      'serious', 'important', 'deadline', 'time-sensitive']
        }

        # Positive sentiment indicators
        self.positive_keywords = ['thank', 'thanks', 'grateful', 'appreciate',
                                'helpful', 'great', 'excellent', 'amazing', 'perfect']

    def analyze_sentiment(self, text: str) -> Dict:
        """
        Comprehensive sentiment analysis combining multiple approaches.

        Args:
            text: Input text to analyze

        Returns:
            Dictionary containing sentiment scores and analysis
        """
        if not text or not text.strip():
            return self._get_neutral_sentiment()

        text = text.strip()

        # VADER analysis (good for informal text and social media)
        vader_scores = self.vader_analyzer.polarity_scores(text)

        # RoBERTa analysis (more nuanced)
        roberta_sentiment = self._get_roberta_sentiment(text)

        # Emotion analysis
        emotions = self._get_emotions(text)

        # TextBlob analysis (simple baseline)
        blob = TextBlob(text)
        textblob_polarity = blob.sentiment.polarity
        textblob_subjectivity = blob.sentiment.subjectivity

        # Combine analyses for final sentiment
        final_sentiment = self._combine_sentiments(
            vader_scores, roberta_sentiment, textblob_polarity
        )

        # Detect escalation indicators
        escalation_level = self._detect_escalation_indicators(text)

        # Calculate confidence score
        confidence = self._calculate_confidence(
            vader_scores, roberta_sentiment, emotions
        )

        return {
            'sentiment': final_sentiment['label'],
            'confidence': confidence,
            'polarity_score': final_sentiment['score'],
            'emotions': emotions,
            'escalation_level': escalation_level,
            'analysis_details': {
                'vader': vader_scores,
                'roberta': roberta_sentiment,
                'textblob': {
                    'polarity': textblob_polarity,
                    'subjectivity': textblob_subjectivity
                }
            },
            'text_length': len(text),
            'contains_caps': self._has_excessive_caps(text),
            'contains_punctuation': self._has_excessive_punctuation(text)
        }

    def _get_roberta_sentiment(self, text: str) -> Dict:
        """Get sentiment from RoBERTa model."""
        if not self.sentiment_pipeline:
            return {'label': 'NEUTRAL', 'score': 0.5}

        try:
            results = self.sentiment_pipeline(text)
            # Find the highest scoring sentiment
            best_result = max(results, key=lambda x: x['score'])
            return {
                'label': best_result['label'],
                'score': best_result['score'],
                'all_scores': results
            }
        except Exception as e:
            logger.error(f"Error in RoBERTa sentiment analysis: {e}")
            return {'label': 'NEUTRAL', 'score': 0.5}

    def _get_emotions(self, text: str) -> Dict:
        """Get emotion analysis from the text."""
        if not self.emotion_pipeline:
            return {'primary_emotion': 'neutral', 'emotion_scores': {}}

        try:
            results = self.emotion_pipeline(text)
            emotion_scores = {result['label']: result['score'] for result in results}
            primary_emotion = max(emotion_scores.items(), key=lambda x: x[1])

            return {
                'primary_emotion': primary_emotion[0],
                'primary_emotion_score': primary_emotion[1],
                'emotion_scores': emotion_scores
            }
        except Exception as e:
            logger.error(f"Error in emotion analysis: {e}")
            return {'primary_emotion': 'neutral', 'emotion_scores': {}}

    def _combine_sentiments(self, vader_scores: Dict, roberta_sentiment: Dict,
                          textblob_polarity: float) -> Dict:
        """Combine different sentiment analyses for final result."""

        # Convert VADER compound score to sentiment label
        vader_compound = vader_scores['compound']
        if vader_compound >= 0.05:
            vader_label = 'POSITIVE'
        elif vader_compound <= -0.05:
            vader_label = 'NEGATIVE'
        else:
            vader_label = 'NEUTRAL'

        # Convert TextBlob polarity to label
        if textblob_polarity > 0.1:
            textblob_label = 'POSITIVE'
        elif textblob_polarity < -0.1:
            textblob_label = 'NEGATIVE'
        else:
            textblob_label = 'NEUTRAL'

        # Voting mechanism
        sentiments = [vader_label, roberta_sentiment['label'], textblob_label]
        sentiment_counts = {
            'POSITIVE': sentiments.count('POSITIVE'),
            'NEGATIVE': sentiments.count('NEGATIVE'),
            'NEUTRAL': sentiments.count('NEUTRAL')
        }

        final_label = max(sentiment_counts.items(), key=lambda x: x[1])[0]

        # Calculate combined score
        scores = [
            vader_compound,
            roberta_sentiment['score'] * (1 if roberta_sentiment['label'] == 'POSITIVE'
                                        else -1 if roberta_sentiment['label'] == 'NEGATIVE' else 0),
            textblob_polarity
        ]
        combined_score = sum(scores) / len(scores)

        return {
            'label': final_label,
            'score': combined_score
        }

    def _detect_escalation_indicators(self, text: str) -> str:
        """Detect escalation level based on keywords and patterns."""
        text_lower = text.lower()

        # Check for high escalation keywords
        high_count = sum(1 for keyword in self.escalation_keywords['high']
                        if keyword in text_lower)

        # Check for medium escalation keywords
        medium_count = sum(1 for keyword in self.escalation_keywords['medium']
                          if keyword in text_lower)

        # Check for urgent keywords
        urgent_count = sum(1 for keyword in self.escalation_keywords['urgent']
                          if keyword in text_lower)

        # Check for excessive caps and punctuation
        caps_ratio = sum(1 for c in text if c.isupper()) / len(text) if text else 0
        exclamation_count = text.count('!')

        # Determine escalation level
        if (high_count >= 2 or urgent_count >= 1 or caps_ratio > 0.3 or
            exclamation_count >= 3):
            return 'high'
        elif high_count >= 1 or medium_count >= 2 or caps_ratio > 0.15:
            return 'medium'
        elif medium_count >= 1:
            return 'low'
        else:
            return 'none'

    def _calculate_confidence(self, vader_scores: Dict, roberta_sentiment: Dict,
                            emotions: Dict) -> float:
        """Calculate confidence score for the sentiment analysis."""

        # VADER confidence (based on compound score magnitude)
        vader_confidence = abs(vader_scores['compound'])

        # RoBERTa confidence
        roberta_confidence = roberta_sentiment.get('score', 0.5)

        # Emotion confidence
        emotion_confidence = emotions.get('primary_emotion_score', 0.5)

        # Average confidence
        confidence = (vader_confidence + roberta_confidence + emotion_confidence) / 3

        return min(confidence, 1.0)

    def _has_excessive_caps(self, text: str) -> bool:
        """Check if text has excessive capital letters."""
        if not text:
            return False
        caps_ratio = sum(1 for c in text if c.isupper()) / len(text)
        return caps_ratio > 0.3

    def _has_excessive_punctuation(self, text: str) -> bool:
        """Check if text has excessive punctuation."""
        punctuation_count = len(re.findall(r'[!?]{2,}', text))
        return punctuation_count > 0

    def _get_neutral_sentiment(self) -> Dict:
        """Return neutral sentiment for empty or invalid input."""
        return {
            'sentiment': 'neutral',
            'confidence': 0.5,
            'polarity_score': 0.0,
            'emotions': {'primary_emotion': 'neutral', 'emotion_scores': {}},
            'escalation_level': 'none',
            'analysis_details': {},
            'text_length': 0,
            'contains_caps': False,
            'contains_punctuation': False
        }


# Global sentiment analyzer instance
_sentiment_analyzer = None

def get_sentiment_analyzer() -> SentimentAnalyzer:
    """Get or create the global sentiment analyzer instance."""
    global _sentiment_analyzer
    if _sentiment_analyzer is None:
        _sentiment_analyzer = SentimentAnalyzer()
    return _sentiment_analyzer


def analyze_sentiment(text: str) -> Dict:
    """
    Main function for sentiment analysis.

    Args:
        text: Input text to analyze

    Returns:
        Dictionary containing sentiment analysis results
    """
    try:
        analyzer = get_sentiment_analyzer()
        return analyzer.analyze_sentiment(text)
    except Exception as e:
        logger.error(f"Error in sentiment analysis: {e}")
        return {
            'sentiment': 'neutral',
            'confidence': 0.0,
            'error': str(e)
        }


def get_sentiment_summary(conversation_history: List[Dict]) -> Dict:
    """
    Analyze sentiment trends across a conversation.

    Args:
        conversation_history: List of conversation turns with sentiment data

    Returns:
        Dictionary containing sentiment trends and insights
    """
    if not conversation_history:
        return {'trend': 'neutral', 'escalation_risk': 'low'}

    sentiments = []
    escalation_levels = []

    for turn in conversation_history:
        if 'sentiment' in turn:
            sentiments.append(turn['sentiment'])
        if 'escalation' in turn:
            escalation_levels.append(turn['escalation'])

    # Calculate trend
    if not sentiments:
        return {'trend': 'neutral', 'escalation_risk': 'low'}

    negative_count = sentiments.count('negative')
    positive_count = sentiments.count('positive')

    if negative_count > positive_count:
        trend = 'declining'
    elif positive_count > negative_count:
        trend = 'improving'
    else:
        trend = 'stable'

    # Calculate escalation risk
    high_escalation_count = escalation_levels.count('high')
    medium_escalation_count = escalation_levels.count('medium')

    if high_escalation_count > 0:
        escalation_risk = 'high'
    elif medium_escalation_count > 1:
        escalation_risk = 'medium'
    else:
        escalation_risk = 'low'

    return {
        'trend': trend,
        'escalation_risk': escalation_risk,
        'sentiment_distribution': {
            'positive': positive_count,
            'negative': negative_count,
            'neutral': sentiments.count('neutral')
        },
        'total_turns': len(conversation_history)
    }
