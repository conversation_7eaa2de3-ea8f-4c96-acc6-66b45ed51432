# Core Framework
streamlit>=1.28.0
gradio>=3.50.0

# RAG and Vector Database
chromadb>=0.4.15
sentence-transformers>=2.2.2
langchain>=0.0.350
langchain-community>=0.0.10

# ML and NLP
transformers>=4.35.0
torch>=2.1.0
torchvision>=0.16.0
torchaudio>=2.1.0
datasets>=2.14.0

# Sentiment Analysis and Emotion Detection
vaderSentiment>=3.3.2
textblob>=0.17.1

# Data Processing
pandas>=2.1.0
numpy>=1.24.0
scikit-learn>=1.3.0

# Evaluation and Metrics
ragas>=0.0.22
evaluate>=0.4.1

# Visualization and UI
plotly>=5.17.0
matplotlib>=3.7.0
seaborn>=0.12.0

# Utilities
python-dotenv>=1.0.0
pydantic>=2.4.0
typing-extensions>=4.8.0

# Optional: OpenAI for advanced features
openai>=1.3.0

# Development and Testing
pytest>=7.4.0
black>=23.9.0
flake8>=6.1.0
