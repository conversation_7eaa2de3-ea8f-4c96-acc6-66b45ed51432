"""
Escalation Detection and Pattern Recognition Module
Analyzes conversation patterns to predict and detect escalation scenarios.
"""
import logging
from typing import List, Dict, Tuple, Optional
from datetime import datetime, timedelta
import re

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class EscalationDetector:
    """
    Detects escalation patterns in customer support conversations.
    Uses multiple signals including sentiment trends, keyword patterns,
    conversation length, and response times.
    """

    def __init__(self):
        # Escalation trigger keywords categorized by severity
        self.escalation_triggers = {
            'critical': [
                'manager', 'supervisor', 'escalate', 'complaint', 'legal action',
                'lawsuit', 'attorney', 'lawyer', 'unacceptable', 'ridiculous',
                'cancel my account', 'close my account', 'refund everything',
                'never again', 'worst service', 'terrible company'
            ],
            'high': [
                'frustrated', 'angry', 'disappointed', 'upset', 'furious',
                'outraged', 'disgusted', 'hate this', 'fed up', 'had enough',
                'waste of time', 'useless', 'horrible', 'awful', 'terrible'
            ],
            'medium': [
                'concerned', 'worried', 'confused', 'bothered', 'annoyed',
                'irritated', 'not happy', 'dissatisfied', 'problem',
                'issue', 'trouble', 'difficulty', 'struggling'
            ],
            'urgency': [
                'urgent', 'emergency', 'asap', 'immediately', 'right now',
                'critical', 'serious', 'important', 'deadline', 'time sensitive',
                'losing money', 'business impact', 'can\'t work'
            ]
        }

        # Positive resolution indicators
        self.resolution_indicators = [
            'thank you', 'thanks', 'helpful', 'solved', 'fixed', 'working',
            'appreciate', 'grateful', 'perfect', 'excellent', 'great',
            'satisfied', 'resolved', 'better now'
        ]

        # Repetition patterns that indicate escalation
        self.repetition_patterns = [
            'i already told you', 'i said before', 'as i mentioned',
            'i keep telling you', 'how many times', 'repeatedly',
            'again and again', 'over and over'
        ]

        # Threat patterns
        self.threat_patterns = [
            'social media', 'twitter', 'facebook', 'review', 'rating',
            'better business bureau', 'bbb', 'consumer protection',
            'report you', 'file a complaint'
        ]

    def analyze_escalation_risk(self, conversation_history: List[Dict],
                              current_message: str) -> Dict:
        """
        Comprehensive escalation risk analysis.

        Args:
            conversation_history: List of previous conversation turns
            current_message: Current user message

        Returns:
            Dictionary containing escalation analysis and recommendations
        """

        # Initialize analysis results
        analysis = {
            'escalation_level': 'none',
            'escalation_score': 0.0,
            'risk_factors': [],
            'triggers_found': [],
            'conversation_metrics': {},
            'recommendations': [],
            'requires_human': False,
            'priority_level': 'normal'
        }

        # Analyze current message
        current_analysis = self._analyze_message(current_message)
        analysis['current_message_score'] = current_analysis['score']
        analysis['triggers_found'].extend(current_analysis['triggers'])

        # Analyze conversation history
        if conversation_history:
            history_analysis = self._analyze_conversation_history(conversation_history)
            analysis['conversation_metrics'] = history_analysis

            # Calculate trend-based escalation
            trend_score = self._calculate_trend_escalation(conversation_history)
            analysis['trend_score'] = trend_score

            # Check for repetitive issues
            repetition_score = self._check_repetitive_patterns(conversation_history, current_message)
            analysis['repetition_score'] = repetition_score

            # Calculate total escalation score
            total_score = (
                current_analysis['score'] * 0.4 +
                trend_score * 0.3 +
                repetition_score * 0.2 +
                history_analysis.get('avg_escalation_score', 0) * 0.1
            )
        else:
            total_score = current_analysis['score']

        analysis['escalation_score'] = min(total_score, 1.0)

        # Determine escalation level and actions
        analysis.update(self._determine_escalation_level(analysis['escalation_score'],
                                                       analysis['triggers_found']))

        # Generate recommendations
        analysis['recommendations'] = self._generate_recommendations(analysis)

        logger.info(f"Escalation analysis complete. Level: {analysis['escalation_level']}, "
                   f"Score: {analysis['escalation_score']:.2f}")

        return analysis

    def _analyze_message(self, message: str) -> Dict:
        """Analyze a single message for escalation indicators."""
        if not message:
            return {'score': 0.0, 'triggers': []}

        message_lower = message.lower()
        score = 0.0
        triggers = []

        # Check for escalation keywords
        for severity, keywords in self.escalation_triggers.items():
            for keyword in keywords:
                if keyword in message_lower:
                    triggers.append({'keyword': keyword, 'severity': severity})

                    # Assign scores based on severity
                    if severity == 'critical':
                        score += 0.3
                    elif severity == 'high':
                        score += 0.2
                    elif severity == 'medium':
                        score += 0.1
                    elif severity == 'urgency':
                        score += 0.15

        # Check for threat patterns
        for pattern in self.threat_patterns:
            if pattern in message_lower:
                triggers.append({'keyword': pattern, 'severity': 'threat'})
                score += 0.25

        # Check for repetition patterns
        for pattern in self.repetition_patterns:
            if pattern in message_lower:
                triggers.append({'keyword': pattern, 'severity': 'repetition'})
                score += 0.15

        # Check for excessive caps (indicates shouting)
        caps_ratio = sum(1 for c in message if c.isupper()) / len(message) if message else 0
        if caps_ratio > 0.3:
            triggers.append({'keyword': 'excessive_caps', 'severity': 'formatting'})
            score += 0.1

        # Check for excessive punctuation
        exclamation_count = message.count('!')
        if exclamation_count >= 3:
            triggers.append({'keyword': 'excessive_punctuation', 'severity': 'formatting'})
            score += 0.1

        return {'score': min(score, 1.0), 'triggers': triggers}

    def _analyze_conversation_history(self, history: List[Dict]) -> Dict:
        """Analyze conversation history for escalation patterns."""
        if not history:
            return {}

        metrics = {
            'total_turns': len(history),
            'user_turns': len([turn for turn in history if 'user' in turn]),
            'avg_escalation_score': 0.0,
            'sentiment_trend': 'stable',
            'escalation_trend': 'stable',
            'conversation_length_minutes': 0,
            'unresolved_issues': 0
        }

        # Calculate average escalation score
        escalation_scores = []
        sentiments = []

        for turn in history:
            if 'escalation' in turn and isinstance(turn['escalation'], dict):
                escalation_scores.append(turn['escalation'].get('escalation_score', 0))

            if 'sentiment' in turn:
                sentiment = turn['sentiment']
                if isinstance(sentiment, dict):
                    sentiment = sentiment.get('sentiment', 'neutral')
                sentiments.append(sentiment)

        if escalation_scores:
            metrics['avg_escalation_score'] = sum(escalation_scores) / len(escalation_scores)

        # Analyze sentiment trend
        if len(sentiments) >= 3:
            recent_sentiments = sentiments[-3:]
            negative_count = recent_sentiments.count('negative')
            if negative_count >= 2:
                metrics['sentiment_trend'] = 'declining'
            elif 'positive' in recent_sentiments[-1:]:
                metrics['sentiment_trend'] = 'improving'

        # Check for unresolved issues (repeated similar messages)
        user_messages = [turn.get('user', '') for turn in history if 'user' in turn]
        if len(user_messages) >= 2:
            similar_messages = self._find_similar_messages(user_messages)
            metrics['unresolved_issues'] = len(similar_messages)

        return metrics

    def _calculate_trend_escalation(self, history: List[Dict]) -> float:
        """Calculate escalation based on conversation trends."""
        if len(history) < 2:
            return 0.0

        # Look at recent sentiment and escalation trends
        recent_turns = history[-5:]  # Last 5 turns

        negative_sentiments = 0
        escalation_mentions = 0

        for turn in recent_turns:
            # Check sentiment
            sentiment = turn.get('sentiment', 'neutral')
            if isinstance(sentiment, dict):
                sentiment = sentiment.get('sentiment', 'neutral')

            if sentiment == 'negative':
                negative_sentiments += 1

            # Check for escalation keywords in user messages
            user_message = turn.get('user', '')
            if user_message:
                message_analysis = self._analyze_message(user_message)
                if message_analysis['score'] > 0.1:
                    escalation_mentions += 1

        # Calculate trend score
        trend_score = (negative_sentiments + escalation_mentions) / len(recent_turns)
        return min(trend_score, 1.0)

    def _check_repetitive_patterns(self, history: List[Dict], current_message: str) -> float:
        """Check for repetitive issues indicating frustration."""
        if not history or not current_message:
            return 0.0

        user_messages = [turn.get('user', '') for turn in history if 'user' in turn]
        user_messages.append(current_message)

        if len(user_messages) < 2:
            return 0.0

        # Simple similarity check based on common words
        current_words = set(current_message.lower().split())
        repetition_score = 0.0

        for prev_message in user_messages[-3:-1]:  # Check last 2 messages
            prev_words = set(prev_message.lower().split())

            if len(current_words) > 0 and len(prev_words) > 0:
                similarity = len(current_words.intersection(prev_words)) / len(current_words.union(prev_words))
                if similarity > 0.5:  # High similarity indicates repetition
                    repetition_score += 0.3

        return min(repetition_score, 1.0)

    def _find_similar_messages(self, messages: List[str]) -> List[Tuple[str, str]]:
        """Find similar messages in conversation history."""
        similar_pairs = []

        for i, msg1 in enumerate(messages):
            for j, msg2 in enumerate(messages[i+1:], i+1):
                words1 = set(msg1.lower().split())
                words2 = set(msg2.lower().split())

                if len(words1) > 0 and len(words2) > 0:
                    similarity = len(words1.intersection(words2)) / len(words1.union(words2))
                    if similarity > 0.6:
                        similar_pairs.append((msg1, msg2))

        return similar_pairs

    def _determine_escalation_level(self, score: float, triggers: List[Dict]) -> Dict:
        """Determine escalation level and required actions."""

        # Check for critical triggers
        critical_triggers = [t for t in triggers if t.get('severity') == 'critical']
        threat_triggers = [t for t in triggers if t.get('severity') == 'threat']

        if critical_triggers or threat_triggers or score >= 0.8:
            return {
                'escalation_level': 'critical',
                'requires_human': True,
                'priority_level': 'urgent'
            }
        elif score >= 0.6:
            return {
                'escalation_level': 'high',
                'requires_human': True,
                'priority_level': 'high'
            }
        elif score >= 0.4:
            return {
                'escalation_level': 'medium',
                'requires_human': False,
                'priority_level': 'medium'
            }
        elif score >= 0.2:
            return {
                'escalation_level': 'low',
                'requires_human': False,
                'priority_level': 'normal'
            }
        else:
            return {
                'escalation_level': 'none',
                'requires_human': False,
                'priority_level': 'normal'
            }

    def _generate_recommendations(self, analysis: Dict) -> List[str]:
        """Generate recommendations based on escalation analysis."""
        recommendations = []

        level = analysis['escalation_level']
        score = analysis['escalation_score']

        if level == 'critical':
            recommendations.extend([
                "Immediately escalate to human agent",
                "Use empathetic and apologetic tone",
                "Offer immediate callback or priority support",
                "Consider offering compensation or goodwill gesture"
            ])
        elif level == 'high':
            recommendations.extend([
                "Consider escalating to human agent",
                "Use very empathetic language",
                "Acknowledge frustration explicitly",
                "Provide clear timeline for resolution"
            ])
        elif level == 'medium':
            recommendations.extend([
                "Use empathetic tone",
                "Acknowledge the customer's concerns",
                "Provide detailed explanation of next steps"
            ])
        elif level == 'low':
            recommendations.extend([
                "Monitor conversation closely",
                "Use supportive language",
                "Ensure clear communication"
            ])

        # Add specific recommendations based on triggers
        triggers = analysis.get('triggers_found', [])
        if any(t.get('severity') == 'repetition' for t in triggers):
            recommendations.append("Address repetitive concerns directly")

        if any(t.get('severity') == 'threat' for t in triggers):
            recommendations.append("Document threats and follow escalation protocol")

        return recommendations


# Global escalation detector instance
_escalation_detector = None

def get_escalation_detector() -> EscalationDetector:
    """Get or create the global escalation detector instance."""
    global _escalation_detector
    if _escalation_detector is None:
        _escalation_detector = EscalationDetector()
    return _escalation_detector


def check_escalation(history: List[Dict], user_input: str) -> Dict:
    """
    Main function for escalation detection.

    Args:
        history: Conversation history
        user_input: Current user message

    Returns:
        Dictionary containing escalation analysis
    """
    try:
        detector = get_escalation_detector()
        return detector.analyze_escalation_risk(history, user_input)
    except Exception as e:
        logger.error(f"Error in escalation detection: {e}")
        return {
            'escalation_level': 'none',
            'escalation_score': 0.0,
            'error': str(e),
            'requires_human': False
        }


def should_escalate_to_human(escalation_analysis: Dict) -> bool:
    """
    Determine if conversation should be escalated to human agent.

    Args:
        escalation_analysis: Result from check_escalation

    Returns:
        Boolean indicating if human escalation is needed
    """
    return escalation_analysis.get('requires_human', False)
